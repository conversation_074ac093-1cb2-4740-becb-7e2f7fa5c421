# Changelog

## v2.0 - Fixed Response Detection (Latest)

### 🐛 **Bug Fix: Corrected Response Detection Logic**

The previous response detection method using file input elements was unreliable. The new implementation uses a much more robust approach:

#### **Old Method (Buggy):**
- Looked for `input[type="file"][multiple]` elements
- Checked for `div[role="button"]` with `aria-disabled="false"`
- Complex parent/child element navigation
- Unreliable and prone to timing issues

#### **New Method (Fixed):**
- **Before sending prompt**: Finds the "New chat" button using `//div[@tabindex='0' and contains(text(), 'New chat')]`
- **After sending prompt**: Monitors the button text every second
- **Response starts**: When text changes from "New chat" to anything else
- **Response completes**: When text returns to "New chat"

### **Key Improvements:**

1. **Reliable Detection**: Uses the button that DeepSeek actually updates during response generation
2. **Handles Dynamic Classes**: Uses `tabindex` and text content instead of CSS classes that change
3. **Two-Phase Detection**: Detects both response start and completion
4. **Better Error Handling**: Re-finds button if reference is lost
5. **Clear Progress Messages**: Shows when response generation starts and completes

### **Technical Changes:**

- Updated `send_prompt()` to return reference to "New chat" button
- Added `find_new_chat_button()` function with XPath selector
- Replaced `wait_for_response_ready()` with new monitoring logic
- Added `wait_for_response_completion()` for second phase detection
- Updated main flow to use the new button-based detection

### **Testing:**

All tests pass with the new logic:
```
✓ Import Test: All new functions are available
✓ XPath Logic Test: Handles dynamic classes correctly
✓ Text Change Detection: Properly detects start and completion
```

This fix should make the tool much more reliable for detecting when DeepSeek has finished generating responses.

---

## v1.0 - Initial Python Implementation

- Created Python version of the Rust CLI tool
- Session persistence with undetected-chromedriver
- CLI interface with argparse
- Basic response detection (later fixed in v2.0)
