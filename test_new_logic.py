#!/usr/bin/env python3
"""
Test the new response detection logic without running the full browser automation
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_xpath_logic():
    """Test the XPath logic for finding the New chat button"""
    
    # Simulate the XPath we're using
    xpath = "//div[@tabindex='0' and contains(text(), 'New chat')]"
    
    print("Testing XPath logic:")
    print(f"XPath: {xpath}")
    print("This should match elements like:")
    print('  <div class="d8ed659a" tabindex="0" style="outline: none;">New chat</div>')
    print('  <div class="random123" tabindex="0">New chat</div>')
    print('  <div tabindex="0">New chat</div>')
    print()
    
    # Test the text change detection logic
    test_cases = [
        ("New chat", "New chat", False, "No change - should continue waiting"),
        ("New chat", "Generating...", True, "Text changed - response started"),
        ("Generating...", "New chat", True, "Back to 'New chat' - response completed"),
        ("New chat", "", True, "Empty text - response started"),
        ("", "New chat", True, "Back to 'New chat' - response completed"),
    ]
    
    print("Testing text change detection:")
    for old_text, new_text, should_trigger, description in test_cases:
        changed = new_text.strip() != "New chat"
        completed = new_text.strip() == "New chat" and old_text.strip() != "New chat"
        
        print(f"  '{old_text}' -> '{new_text}': {description}")
        if should_trigger:
            if changed and new_text.strip() != "New chat":
                print("    ✓ Would detect response start")
            elif completed:
                print("    ✓ Would detect response completion")
        else:
            print("    ✓ Would continue waiting")
    
    return True

def test_imports():
    """Test that we can import the updated module"""
    try:
        import deepseek_wrapper
        
        # Check that the new functions exist
        assert hasattr(deepseek_wrapper, 'find_new_chat_button')
        assert hasattr(deepseek_wrapper, 'wait_for_response_completion')
        
        print("✓ All new functions are available")
        return True
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def main():
    print("Testing Updated DeepSeek Wrapper Logic")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("XPath Logic Test", test_xpath_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ Updated logic looks good!")
        print("\nKey improvements:")
        print("  • More reliable response detection using 'New chat' button")
        print("  • Handles dynamic CSS classes by using tabindex and text")
        print("  • Detects both response start and completion")
        print("  • Better error handling if button reference is lost")
    else:
        print("✗ Some tests failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
