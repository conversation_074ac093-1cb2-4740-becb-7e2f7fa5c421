#!/usr/bin/env python3
"""
DeepSeek Website Wrapper - Python Version
A CLI tool to interact with DeepSeek chat using undetected chromedriver
"""

import argparse
import os
import sys
import time
import pyperclip
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc


def setup_driver():
    """Setup undetected chromedriver with session persistence"""
    print("Starting undetected chromedriver...")
    
    # Create user data directory for session persistence
    user_data_dir = Path.home() / ".deepseek_wrapper" / "chrome_profile"
    user_data_dir.mkdir(parents=True, exist_ok=True)
    
    options = uc.ChromeOptions()
    options.add_argument(f"--user-data-dir={user_data_dir}")
    options.add_argument("--no-first-run")
    options.add_argument("--no-default-browser-check")
    
    try:
        driver = uc.Chrome(options=options)
        return driver
    except Exception as e:
        print(f"Failed to start chromedriver: {e}")
        sys.exit(1)


def wait_for_deepseek_ready(driver, timeout=60):
    """Wait for the 'Hi, I'm DeepSeek.' text to appear on the page"""
    print("Waiting for DeepSeek to load...")
    
    for attempt in range(timeout):
        try:
            # Check if we can find an element containing "Hi, I'm DeepSeek."
            elements = driver.find_elements(By.XPATH, "//*[contains(text(), \"Hi, I'm DeepSeek.\")]")
            if elements:
                print("DeepSeek is ready!")
                return True
        except Exception:
            pass
        
        time.sleep(1)
    
    raise TimeoutException("Timeout waiting for DeepSeek to load")


def send_prompt(driver, prompt):
    """Send the prompt to the chat input field"""
    print("Sending prompt...")
    
    try:
        # Find the chat input element by ID
        chat_input = driver.find_element(By.ID, "chat-input")
        
        # Clear any existing text and send the prompt
        chat_input.clear()
        chat_input.send_keys(prompt)
        
        # Send Enter key to submit
        chat_input.send_keys(Keys.RETURN)
        
    except NoSuchElementException:
        raise Exception("Failed to find chat input element")
    except Exception as e:
        raise Exception(f"Failed to send prompt: {e}")


def wait_for_response_ready(driver, timeout_seconds=300):
    """Wait for the response to be ready by checking the specified element conditions"""
    print("Waiting for response...")
    max_attempts = timeout_seconds
    
    for attempt in range(max_attempts):
        if attempt % 10 == 0 and attempt > 0:
            print(f"Still waiting for response... ({attempt}/{max_attempts}s)")
        
        try:
            # Find the file input element
            file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type=\"file\"][multiple]")
            
            if file_inputs:
                # Look for a div with role="button" near the file input
                # Since we can't easily get parent in this context, search globally
                buttons = driver.find_elements(By.CSS_SELECTOR, "div[role=\"button\"]")
                
                for button in buttons:
                    try:
                        aria_disabled = button.get_attribute("aria-disabled")
                        if aria_disabled == "false":
                            print("Response is ready!")
                            return True
                    except Exception:
                        continue
        except Exception:
            pass
        
        time.sleep(1)
    
    raise TimeoutException("Timeout waiting for response to be ready")


def get_response(driver):
    """Get the response by finding all .ds-flex elements, clicking the last one's first .ds-icon-button,
    and then reading from clipboard"""
    print("Getting response...")
    
    try:
        # Find all elements with class "ds-flex"
        ds_flex_elements = driver.find_elements(By.CSS_SELECTOR, ".ds-flex")
        
        if not ds_flex_elements:
            raise Exception("No .ds-flex elements found")
        
        # Get the last .ds-flex element
        last_element = ds_flex_elements[-1]
        
        # Find the first .ds-icon-button within this element
        icon_buttons = last_element.find_elements(By.CSS_SELECTOR, ".ds-icon-button")
        
        if not icon_buttons:
            raise Exception("No .ds-icon-button elements found in the last .ds-flex element")
        
        first_icon_button = icon_buttons[0]
        
        # Click the button
        first_icon_button.click()
        
        # Wait 1 second as specified
        time.sleep(1)
        
        # Read from clipboard
        try:
            clipboard_content = pyperclip.paste()
            return clipboard_content
        except Exception as e:
            raise Exception(f"Failed to read from clipboard: {e}")
            
    except Exception as e:
        raise Exception(f"Failed to get response: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="A CLI tool to interact with DeepSeek chat using undetected chromedriver"
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--prompt", type=str, help="Prompt text to send to DeepSeek")
    group.add_argument("--prompt-file", type=str, help="File containing the prompt to send to DeepSeek")
    
    parser.add_argument(
        "--timeout", 
        type=int, 
        default=300, 
        help="Timeout in seconds for waiting for responses (default: 300)"
    )
    
    args = parser.parse_args()
    
    # Get the prompt from either command line or file
    if args.prompt:
        prompt = args.prompt
    else:
        try:
            with open(args.prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
        except FileNotFoundError:
            print(f"Error: File '{args.prompt_file}' not found")
            sys.exit(1)
        except Exception as e:
            print(f"Error reading file '{args.prompt_file}': {e}")
            sys.exit(1)
    
    # Setup the driver
    driver = setup_driver()
    
    try:
        # Navigate to DeepSeek chat
        print("Navigating to DeepSeek chat...")
        driver.get("https://chat.deepseek.com/")
        
        # Wait for the "Hi, I'm DeepSeek." text to appear
        wait_for_deepseek_ready(driver)
        
        # Wait 500ms as specified
        time.sleep(0.5)
        
        # Send the prompt to the chat input
        send_prompt(driver, prompt)
        
        # Wait for response to be ready
        wait_for_response_ready(driver, args.timeout)
        
        # Get the response and copy to clipboard
        response = get_response(driver)
        
        # Print response to stdout
        print("Response:")
        print(response)
        
        # Wait for user confirmation before exiting
        input("Press Enter to exit and close the browser...")
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        # Quit the driver
        try:
            driver.quit()
        except Exception:
            pass


if __name__ == "__main__":
    main()
