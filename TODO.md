# 

this should be a new rust cli applications using clap, it gets --prompt or --prompt-file as input


it should start the undetected chromedriver as detailed below, BUT with a session, so on restarts, the same cookies and local storage are used.
you browse to https://chat.deepseek.com/ and wait until you detect a node with text "Hi, I'm DeepSeek." - check every second for this
once done, you wait 500ms, then you send the prompt to #chat-input and then send Enter to that textarea.

after sending, you wait every second and check this element:
<input type="file" multiple=""

get this input, get parent node, then inside that parent search for <div> with role="button" and if its aria-disabled="false"

if it is false, the response is there!

to get the response, get all .ds-flex class elements in page, get the last one, and click its first .ds-icon-button element, wait 1 second, then print the text from clipboard to stdout and wait on user confirmation to exit the driver





Rust undetected chromedriver
A rust implementation of ultrafunkamsterdam's undetected-chromedriver library based on thirtyfour

Installation
To use this library, you will need to have Rust and Cargo installed on your system. You can then add the following line to your Cargo.toml file:

[dependencies]
undetected-chromedriver = "0.1.2"
Usage
Here's an example of how you can use the undetected chromedriver in your Rust project:

use undetected_chromedriver::chrome;
use tokio;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let driver = chrome().await?;

    driver.goto("https://www.rust-lang.org/").await?;

    let title = driver.title().await?;
    println!("Title: {}", title);

    driver.quit().await?;

    Ok(())
}





Rust: A New Frontier in Undetected Browser Automation
Rust, known for its performance and safety, is a rapidly growing language, and its ecosystem for browser automation is evolving to include stealth capabilities.

undetected-chromedriver Crate: For developers familiar with the Selenium ecosystem, a crate named undetected-chromedriver is available.[4] This library is built on top of thirtyfour, a popular and actively developed Selenium WebDriver client for Rust.[7] This provides a direct and familiar path for those coming from other languages.

chromiumoxide_stealth Crate: For those who prefer working directly with the Chrome DevTools Protocol (CDP), similar to how Puppeteer operates, the chromiumoxide crate is a powerful choice. To enhance its stealth capabilities, the chromiumoxide_stealth crate has been developed.[5] It aims to mirror the evasion techniques found in the JavaScript puppeteer-extra-plugin-stealth, making it a strong contender for avoiding bot detection in a more direct, CDP-based approach.
