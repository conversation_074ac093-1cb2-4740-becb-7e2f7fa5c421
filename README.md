# DeepSeek Website Wrapper

A Python CLI tool that automates interaction with DeepSeek's chat interface using undetected chromedriver with session persistence.

## Quick Start

```bash
# Install dependencies
pip3 install -r requirements.txt

# Send a prompt
python3 deepseek_wrapper.py --prompt "Hello! What is 2+2?"

# Or use a file
python3 deepseek_wrapper.py --prompt-file test_prompt.txt
```

## How it works

1. **Session Persistence**: Maintains cookies and local storage across runs
2. **Navigate**: Goes to https://chat.deepseek.com/
3. **Wait for Load**: Waits for "Hi, I'm DeepSeek." text to appear
4. **Send Prompt**: Submits your prompt to the chat input
5. **Monitor Response**: Watches the "New chat" button text changes to detect when response generation starts and completes
6. **Extract Response**: Clicks the copy button and reads from clipboard
7. **Output**: Prints the response to stdout

## Response Detection Logic

The tool uses a reliable method to detect response completion:

1. **Before sending**: Finds the "New chat" button (`<div tabindex="0">New chat</div>`)
2. **After sending**: Monitors the button text every second
3. **Generation starts**: When text changes from "New chat" to something else
4. **Generation complete**: When text returns to "New chat"

This method is much more reliable than the previous file input detection.
