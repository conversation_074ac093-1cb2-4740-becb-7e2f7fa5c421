# DeepSeek Website Wrapper

A Rust CLI application that automates interaction with DeepSeek's chat interface using undetected chromedriver. This tool allows you to send prompts to DeepSeek and get responses programmatically while maintaining session persistence.

## Features

- **Session Persistence**: Uses the same browser session across restarts, preserving cookies and local storage
- **Undetected Browser**: Uses undetected-chromedriver to avoid detection
- **CLI Interface**: Simple command-line interface with clap
- **Flexible Input**: Accept prompts directly or from files
- **Automatic Response Detection**: Waits for response completion and extracts the result
- **Clipboard Integration**: Copies the response to clipboard for easy access

## Installation

Make sure you have <PERSON><PERSON> and <PERSON><PERSON> installed, then:

```bash
git clone <repository-url>
cd deepseek-website-wrapper
cargo build --release
```

## Usage

### Send a prompt directly:
```bash
cargo run -- --prompt "Hello! Can you explain what Rust is?"
```

### Send a prompt from a file:
```bash
cargo run -- --prompt-file test_prompt.txt
```

## How it works

1. **Browser Launch**: Starts undetected chromedriver with session persistence
2. **Navigation**: Navigates to https://chat.deepseek.com/
3. **Wait for Load**: Waits for the "Hi, I'm DeepSeek." text to appear
4. **Send Prompt**: Sends your prompt to the chat input field
5. **Wait for Response**: Monitors for response completion using DOM element checks
6. **Extract Response**: Finds the response, clicks the copy button, and reads from clipboard
7. **Output**: Prints the response to stdout
8. **User Confirmation**: Waits for user input before closing the browser

## Technical Details

- Built with Rust using tokio for async operations
- Uses `undetected-chromedriver` for browser automation
- Uses `thirtyfour` WebDriver API for DOM interaction
- Uses `clap` for CLI argument parsing
- Uses `clipboard` for clipboard operations

## Requirements

- Rust 1.70+ (for async/await support)
- Chrome/Chromium browser installed
- Internet connection

## Error Handling

The application includes comprehensive error handling for:
- Browser startup failures
- Network connectivity issues
- Element detection timeouts
- Clipboard access problems

## Session Persistence

The browser session is automatically persisted, meaning:
- Login state is maintained between runs
- Chat history is preserved
- User preferences are saved
- No need to re-authenticate each time

## Limitations

- Requires a graphical environment (browser needs to run)
- Depends on DeepSeek's website structure (may break if they change their UI)
- Response detection relies on specific DOM elements
- Clipboard access may require permissions on some systems
