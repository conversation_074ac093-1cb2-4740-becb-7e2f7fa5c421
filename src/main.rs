use anyhow::{Context, Result};
use clap::Parser;
use clipboard::ClipboardProvider;
use std::fs;
use std::io::{self, Write};
use std::path::PathBuf;
use std::time::Duration;
// Import types from the thirtyfour version used by undetected-chromedriver
// We need to access them through the re-exports or use extern crate
extern crate thirtyfour;
use thirtyfour::{By, WebDriver};
use tokio::time::sleep;
use undetected_chromedriver::chrome;

#[derive(Parser)]
#[command(name = "deepseek-wrapper")]
#[command(about = "A CLI tool to interact with DeepSeek chat using undetected chromedriver")]
struct Args {
    /// Prompt text to send to DeepSeek
    #[arg(long, conflicts_with = "prompt_file", required_unless_present = "prompt_file")]
    prompt: Option<String>,

    /// File containing the prompt to send to DeepSeek
    #[arg(long, conflicts_with = "prompt", required_unless_present = "prompt")]
    prompt_file: Option<PathBuf>,

    /// Timeout in seconds for waiting for responses (default: 3000)
    #[arg(long, default_value = "3000")]
    timeout: u64,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // Get the prompt from either command line or file
    let prompt = match (args.prompt, args.prompt_file) {
        (Some(prompt), None) => prompt,
        (None, Some(file_path)) => {
            fs::read_to_string(&file_path)
                .with_context(|| format!("Failed to read prompt file: {:?}", file_path))?
        }
        _ => unreachable!(), // clap ensures one of them is present
    };

    println!("Starting undetected chromedriver...");

    // Start the undetected chromedriver with session persistence
    let driver = chrome().await.map_err(|e| anyhow::anyhow!("Failed to start chromedriver: {}", e))?;

    // Navigate to DeepSeek chat
    println!("Navigating to DeepSeek chat...");
    driver
        .goto("https://chat.deepseek.com/")
        .await
        .context("Failed to navigate to DeepSeek")?;

    // Wait for the "Hi, I'm DeepSeek." text to appear
    println!("Waiting for DeepSeek to load...");
    wait_for_deepseek_ready(&driver).await?;

    // Wait 500ms as specified
    sleep(Duration::from_millis(500)).await;

    // Send the prompt to the chat input
    println!("Sending prompt...");
    send_prompt(&driver, &prompt).await?;

    // Wait for response to be ready
    println!("Waiting for response...");
    wait_for_response_ready(&driver, args.timeout).await?;

    // Get the response and copy to clipboard
    println!("Getting response...");
    let response = get_response(&driver).await?;

    // Print response to stdout
    println!("Response:");
    println!("{}", response);

    // Wait for user confirmation before exiting
    print!("Press Enter to exit and close the browser...");
    io::stdout().flush().unwrap();
    let mut input = String::new();
    io::stdin().read_line(&mut input).unwrap();

    // Quit the driver
    driver.quit().await.context("Failed to quit driver")?;

    Ok(())
}

/// Wait for the "Hi, I'm DeepSeek." text to appear on the page
async fn wait_for_deepseek_ready(driver: &WebDriver) -> Result<()> {
    let max_attempts = 60; // Wait up to 60 seconds

    for _ in 0..max_attempts {
        // Check if we can find an element containing "Hi, I'm DeepSeek."
        let elements = driver
            .find_all(By::XPath("//*[contains(text(), \"Hi, I'm DeepSeek.\")]"))
            .await
            .unwrap_or_default();

        if !elements.is_empty() {
            println!("DeepSeek is ready!");
            return Ok(());
        }

        sleep(Duration::from_secs(1)).await;
    }

    anyhow::bail!("Timeout waiting for DeepSeek to load")
}

/// Send the prompt to the chat input field
async fn send_prompt(driver: &WebDriver, prompt: &str) -> Result<()> {
    // Find the chat input element by ID
    let chat_input = driver
        .find(By::Id("chat-input"))
        .await
        .context("Failed to find chat input element")?;

    // Clear any existing text and send the prompt
    chat_input.clear().await.context("Failed to clear chat input")?;
    chat_input
        .send_keys(prompt)
        .await
        .context("Failed to send prompt to chat input")?;

    // Send Enter key to submit
    chat_input
        .send_keys("\n")
        .await
        .context("Failed to send Enter key")?;

    Ok(())
}

/// Wait for the response to be ready by checking the specified element conditions
async fn wait_for_response_ready(driver: &WebDriver, timeout_seconds: u64) -> Result<()> {
    let max_attempts = timeout_seconds; // Wait up to specified timeout for response

    for attempt in 0..max_attempts {
        if attempt % 10 == 0 && attempt > 0 {
            println!("Still waiting for response... ({}/{}s)", attempt, max_attempts);
        }
        // Find the file input element
        let file_inputs = driver
            .find_all(By::Css("input[type=\"file\"][multiple]"))
            .await
            .unwrap_or_default();

        if let Some(_file_input) = file_inputs.first() {
            // Look for a div with role="button" near the file input
            // Since parent() doesn't exist in this version, we'll search globally
            let buttons = driver
                .find_all(By::Css("div[role=\"button\"]"))
                .await
                .unwrap_or_default();

            for button in buttons {
                if let Ok(Some(aria_disabled)) = button.attr("aria-disabled").await {
                    if aria_disabled == "false" {
                        println!("Response is ready!");
                        return Ok(());
                    }
                }
            }
        }

        sleep(Duration::from_secs(1)).await;
    }

    anyhow::bail!("Timeout waiting for response to be ready")
}

/// Get the response by finding all .ds-flex elements, clicking the last one's first .ds-icon-button,
/// and then reading from clipboard
async fn get_response(driver: &WebDriver) -> Result<String> {
    // Find all elements with class "ds-flex"
    let ds_flex_elements = driver
        .find_all(By::Css(".ds-flex"))
        .await
        .context("Failed to find .ds-flex elements")?;

    if ds_flex_elements.is_empty() {
        anyhow::bail!("No .ds-flex elements found");
    }

    // Get the last .ds-flex element
    let last_element = ds_flex_elements
        .last()
        .context("Failed to get last .ds-flex element")?;

    // Find the first .ds-icon-button within this element
    let icon_buttons = last_element
        .find_all(By::Css(".ds-icon-button"))
        .await
        .context("Failed to find .ds-icon-button elements")?;

    if icon_buttons.is_empty() {
        anyhow::bail!("No .ds-icon-button elements found in the last .ds-flex element");
    }

    let first_icon_button = &icon_buttons[0];

    // Click the button
    first_icon_button
        .click()
        .await
        .context("Failed to click the icon button")?;

    // Wait 1 second as specified
    sleep(Duration::from_secs(1)).await;

    // Read from clipboard
    let mut clipboard: clipboard::ClipboardContext = ClipboardProvider::new()
        .map_err(|e| anyhow::anyhow!("Failed to initialize clipboard: {}", e))?;

    let clipboard_content = clipboard
        .get_contents()
        .map_err(|e| anyhow::anyhow!("Failed to read from clipboard: {}", e))?;

    Ok(clipboard_content)
}
